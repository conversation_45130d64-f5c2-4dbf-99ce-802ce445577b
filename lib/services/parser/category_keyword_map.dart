/// Multilingual keyword mappings for category detection
/// Maps category IDs to lists of keywords in English, Spanish, and German
const Map<String, List<String>> categoryKeywords = {
  // Food & Drink
  'food': [
    // English
    'food', 'restaurant', 'cafe', 'coffee', 'lunch', 'dinner', 'breakfast',
    'pizza', 'burger', 'sandwich', 'meal', 'eat', 'drink', 'bar', 'pub',
    'snack', 'grocery', 'supermarket', 'kitchen', 'cooking', 'recipe',
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    // Spanish
    'comida', 'restaurante', 'almuerzo', 'cena', 'desayuno', 'bebida',
    'supermercado', 'cocina', 'comer', 'beber', 'cafeteria', 'bar',
    // German
    'essen', 'restaurant', 'mittagessen', 'abendessen', 'frühstück',
    'trinken', 'supermarkt', 'küche', 'café', 'bar'
  ],

  // Transportation
  'transport': [
    // English
    'transport', 'taxi', 'uber', 'lyft', 'bus', 'train', 'subway', 'metro',
    'gas', 'fuel', 'petrol', 'parking', 'toll', 'car', 'vehicle',
    'airline', 'flight', 'airport', 'ticket', 'travel', 'trip',
    'bike', 'bicycle', 'scooter', 'motorcycle', 'boat', 'ferry',
    // Spanish
    'transporte', 'taxi', 'autobús', 'tren', 'metro', 'gasolina',
    'combustible', 'estacionamiento', 'peaje', 'coche', 'viaje',
    'vuelo', 'aeropuerto', 'billete', 'bicicleta',
    // German
    'transport', 'taxi', 'bus', 'zug', 'u-bahn', 'benzin', 'kraftstoff',
    'parkplatz', 'maut', 'auto', 'fahrzeug', 'flug', 'flughafen',
    'ticket', 'reise', 'fahrrad'
  ],

  // Shopping
  'shopping': [
    // English
    'shopping', 'shop', 'store', 'mall', 'market', 'retail', 'purchase', 'buy',
    'clothes', 'clothing', 'fashion', 'shoes', 'accessories', 'jewelry',
    'electronics', 'gadget', 'phone', 'computer', 'laptop', 'tablet',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m',
    // Spanish
    'compras', 'tienda', 'centro comercial', 'mercado', 'comprar',
    'ropa', 'zapatos', 'accesorios', 'joyería', 'electrónicos',
    'teléfono', 'computadora', 'portátil',
    // German
    'einkaufen', 'geschäft', 'einkaufszentrum', 'markt', 'kaufen',
    'kleidung', 'schuhe', 'accessoires', 'schmuck', 'elektronik',
    'telefon', 'computer', 'laptop'
  ],

  // Utilities
  'utilities': [
    // English
    'electric', 'electricity', 'gas', 'water', 'internet', 'phone',
    'cable', 'tv', 'streaming', 'netflix', 'spotify', 'subscription',
    'utility', 'bill', 'payment', 'service', 'provider',
    'verizon', 'att', 'comcast', 'spectrum', 'hulu', 'disney+',
    // Spanish
    'electricidad', 'agua', 'internet', 'teléfono', 'cable',
    'servicios', 'factura', 'pago', 'proveedor', 'suscripción',
    // German
    'strom', 'elektrizität', 'wasser', 'internet', 'telefon',
    'kabel', 'rechnung', 'zahlung', 'anbieter', 'abonnement'
  ],

  // Entertainment
  'entertainment': [
    // English
    'movie', 'cinema', 'theater', 'concert', 'music', 'game', 'gaming',
    'entertainment', 'fun', 'party', 'club', 'festival', 'event',
    'sport', 'gym', 'fitness', 'recreation', 'hobby', 'leisure',
    'netflix', 'spotify', 'xbox', 'playstation', 'steam',
    // Spanish
    'película', 'cine', 'teatro', 'concierto', 'música', 'juego',
    'entretenimiento', 'diversión', 'fiesta', 'club', 'festival',
    'deporte', 'gimnasio', 'recreación', 'hobby',
    // German
    'film', 'kino', 'theater', 'konzert', 'musik', 'spiel',
    'unterhaltung', 'spaß', 'party', 'club', 'festival',
    'sport', 'fitnessstudio', 'erholung', 'hobby'
  ],

  // Health & Medical
  'health': [
    // English
    'health', 'medical', 'doctor', 'hospital', 'pharmacy', 'medicine',
    'prescription', 'dental', 'dentist', 'clinic', 'treatment',
    'insurance', 'wellness', 'fitness', 'vitamin', 'supplement',
    'cvs', 'walgreens', 'rite aid', 'urgent care',
    // Spanish
    'salud', 'médico', 'hospital', 'farmacia', 'medicina',
    'receta', 'dental', 'dentista', 'clínica', 'tratamiento',
    'seguro', 'bienestar', 'vitamina', 'suplemento',
    // German
    'gesundheit', 'medizinisch', 'arzt', 'krankenhaus', 'apotheke',
    'medizin', 'rezept', 'zahnarzt', 'klinik', 'behandlung',
    'versicherung', 'wellness', 'vitamin', 'nahrungsergänzung'
  ],

  // Education
  'education': [
    // English
    'school', 'university', 'college', 'education', 'tuition', 'course',
    'class', 'book', 'textbook', 'supplies', 'learning', 'study',
    'teacher', 'professor', 'student', 'academic', 'training',
    // Spanish
    'escuela', 'universidad', 'colegio', 'educación', 'matrícula',
    'curso', 'clase', 'libro', 'suministros', 'aprendizaje',
    'maestro', 'profesor', 'estudiante', 'entrenamiento',
    // German
    'schule', 'universität', 'hochschule', 'bildung', 'studiengebühren',
    'kurs', 'klasse', 'buch', 'lehrbuch', 'lernen', 'studium',
    'lehrer', 'professor', 'student', 'ausbildung'
  ],

  // Home & Garden
  'home': [
    // English
    'home', 'house', 'rent', 'mortgage', 'furniture', 'decoration',
    'garden', 'tools', 'repair', 'maintenance', 'cleaning', 'supplies',
    'ikea', 'home depot', 'lowes', 'bed bath beyond',
    // Spanish
    'casa', 'hogar', 'alquiler', 'hipoteca', 'muebles', 'decoración',
    'jardín', 'herramientas', 'reparación', 'mantenimiento', 'limpieza',
    // German
    'haus', 'zuhause', 'miete', 'hypothek', 'möbel', 'dekoration',
    'garten', 'werkzeuge', 'reparatur', 'wartung', 'reinigung'
  ],

  // Personal Care
  'personal': [
    // English
    'beauty', 'cosmetics', 'haircut', 'salon', 'spa', 'massage',
    'personal care', 'hygiene', 'skincare', 'makeup', 'perfume',
    'barber', 'nail', 'manicure', 'pedicure',
    // Spanish
    'belleza', 'cosméticos', 'corte de pelo', 'salón', 'cuidado personal',
    'higiene', 'maquillaje', 'perfume', 'barbero', 'uñas',
    // German
    'schönheit', 'kosmetik', 'haarschnitt', 'salon', 'körperpflege',
    'hygiene', 'hautpflege', 'makeup', 'parfüm', 'friseur', 'nagel'
  ],

  // Business & Professional
  'business': [
    // English
    'business', 'office', 'supplies', 'professional', 'meeting',
    'conference', 'software', 'service', 'consulting', 'legal',
    'accounting', 'tax', 'bank', 'fee', 'commission',
    // Spanish
    'negocio', 'oficina', 'suministros', 'profesional', 'reunión',
    'conferencia', 'software', 'servicio', 'consultoría', 'legal',
    'contabilidad', 'impuesto', 'banco', 'tarifa', 'comisión',
    // German
    'geschäft', 'büro', 'büromaterial', 'professionell', 'besprechung',
    'konferenz', 'software', 'service', 'beratung', 'rechtlich',
    'buchhaltung', 'steuer', 'bank', 'gebühr', 'provision'
  ]
};

/// Utility function to find category by keywords with enhanced conflict resolution
String? findCategoryByKeywords(String text) {
  if (text.isEmpty) return null;

  final normalizedText = text.toLowerCase().trim();

  // Split text into words for matching
  final words = normalizedText.split(RegExp(r'\s+'));

  // Score each category based on keyword matches
  final categoryScores = <String, double>{};
  final matchedKeywords = <String, List<String>>{};

  for (final entry in categoryKeywords.entries) {
    final categoryId = entry.key;
    final keywords = entry.value;
    double score = 0;
    final matched = <String>[];



    // Check if any keyword matches
    for (final keyword in keywords) {
      final keywordLower = keyword.toLowerCase();



      // Brand/proper noun exact matches get very high score (check first)
      if (keywordLower.length > 3 && _isBrandName(keywordLower)) {
        if (normalizedText.contains(keywordLower)) {
          score += 15;
          matched.add(keywordLower);
          continue;
        }
      }

      // Exact word match gets highest score
      if (words.contains(keywordLower)) {


        // Give extra points for longer/more specific keywords
        double baseScore = 10 + (keywordLower.length > 5 ? 3 : 0);

        // Apply category-specific bonuses for food-related keywords
        if (categoryId == 'food' && _isFoodSpecificKeyword(keywordLower)) {
          baseScore += 5; // Bonus for food-specific keywords like 'grocery'
        }

        score += baseScore;
        matched.add(keywordLower);
        continue;
      }

      // Partial word match gets medium score (with improved precision)
      bool partialMatch = false;
      for (final word in words) {
        if (word.length >= 3 && keywordLower.length >= 3) {
          // More restrictive partial matching to avoid false positives
          bool isValidPartialMatch = false;

          if (word.contains(keywordLower)) {
            // Keyword is contained in word - check if it's a meaningful match
            final ratio = keywordLower.length / word.length;
            // Require keyword to be at least 60% of the word length, or be a prefix/suffix (min 5 chars)
            if (ratio >= 0.6 || (keywordLower.length >= 5 && (word.startsWith(keywordLower) || word.endsWith(keywordLower)))) {
              isValidPartialMatch = true;
            }
          } else if (keywordLower.contains(word)) {
            // Word is contained in keyword - check if it's a meaningful match
            final ratio = word.length / keywordLower.length;
            // Require word to be at least 60% of the keyword length, or be a prefix/suffix (min 5 chars)
            if (ratio >= 0.6 || (word.length >= 5 && (keywordLower.startsWith(word) || keywordLower.endsWith(word)))) {
              isValidPartialMatch = true;
            }
          }

          if (isValidPartialMatch) {
            score += 6;
            matched.add(keywordLower);
            partialMatch = true;
            break;
          }
        }
      }

      if (partialMatch) continue;

      // Full text contains keyword gets low score
      if (normalizedText.contains(keywordLower) && keywordLower.length >= 3) {
        score += 2;
        matched.add(keywordLower);
      }
    }

    if (score > 0) {
      categoryScores[categoryId] = score;
      matchedKeywords[categoryId] = matched;


    }
  }

  // Apply compound phrase detection and priority rules
  categoryScores.forEach((categoryId, score) {
    final matched = matchedKeywords[categoryId] ?? [];

    // Detect compound phrases and apply bonuses
    if (_hasCompoundPhrase(normalizedText, categoryId, matched)) {
      categoryScores[categoryId] = score + 8; // Bonus for compound phrases
    }

    // Apply category priority rules for conflicts
    if (_shouldApplyCategoryPriority(normalizedText, categoryId, categoryScores.keys.toList())) {
      categoryScores[categoryId] = score + 6; // Priority bonus
    }
  });

  // Return the category with the highest score, but only if it has a significant score
  if (categoryScores.isEmpty) return null;

  final bestCategory = categoryScores.entries
      .reduce((a, b) => a.value > b.value ? a : b);


  // Only return a match if the score is significant enough
  if (bestCategory.value < 6) return null;

  // Enhanced conflict resolution with improved tie-breaking
  final sortedScores = categoryScores.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));

  if (sortedScores.length > 1) {
    final best = sortedScores[0];
    final second = sortedScores[1];

    // Enhanced tie-breaking logic with category-specific rules
    // Special case: food vs shopping conflict resolution (check even if scores aren't close)
    if ((best.key == 'food' && second.key == 'shopping') ||
        (best.key == 'shopping' && second.key == 'food')) {
      // Check for food-specific indicators in compound phrases
      if (_containsFoodShoppingPhrase(normalizedText)) {
        return 'food'; // Prefer food for "grocery shopping", "food shopping"
      }
      // Check for shopping-specific indicators
      if (_containsShoppingSpecificPhrase(normalizedText)) {
        return 'shopping'; // Prefer shopping for "clothes shopping", "electronics shopping"
      }
    }

    if (best.value < second.value + 4) {
      // If still ambiguous and scores are very close, return null
      if (best.value < second.value + 2 && best.value < 12) {
        return null;
      }
    }
  }

  return bestCategory.key;
}

/// Check if a keyword is a brand name or proper noun
bool _isBrandName(String keyword) {
  final brandKeywords = [
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m', 'uber', 'lyft'
  ];
  return brandKeywords.contains(keyword);
}

/// Check if a keyword is food-specific and should get priority
bool _isFoodSpecificKeyword(String keyword) {
  final foodSpecificKeywords = [
    'grocery', 'supermarket', 'restaurant', 'cafe', 'coffee',
    'meal', 'food', 'kitchen', 'cooking', 'recipe'
  ];
  return foodSpecificKeywords.contains(keyword);
}

/// Detect compound phrases for category bonuses
bool _hasCompoundPhrase(String text, String categoryId, List<String> matchedKeywords) {
  if (categoryId == 'food') {
    // Food compound phrases
    final foodCompounds = [
      'grocery shopping', 'food shopping', 'restaurant meal',
      'coffee shop', 'fast food', 'takeout food'
    ];
    return foodCompounds.any((phrase) => text.contains(phrase));
  }

  if (categoryId == 'shopping') {
    // Shopping compound phrases
    final shoppingCompounds = [
      'clothes shopping', 'electronics shopping', 'online shopping',
      'retail store', 'shopping mall', 'department store'
    ];
    return shoppingCompounds.any((phrase) => text.contains(phrase));
  }

  if (categoryId == 'transport') {
    // Transport compound phrases
    final transportCompounds = [
      'gas station', 'fuel station', 'petrol station', 'service station',
      'car rental', 'taxi ride', 'uber ride', 'lyft ride',
      'bus ticket', 'train ticket', 'flight ticket', 'airline ticket',
      'parking fee', 'toll road', 'car wash', 'auto repair'
    ];
    return transportCompounds.any((phrase) => text.contains(phrase));
  }

  return false;
}

/// Apply category priority rules for conflict resolution
bool _shouldApplyCategoryPriority(String text, String categoryId, List<String> allCategories) {
  // Food category gets priority when both food and shopping keywords are present
  if (categoryId == 'food' && allCategories.contains('shopping')) {
    final foodPriorityIndicators = [
      'grocery', 'restaurant', 'cafe', 'meal', 'food', 'eat', 'drink'
    ];
    return foodPriorityIndicators.any((indicator) => text.contains(indicator));
  }

  return false;
}

/// Check for food-shopping compound phrases that should prefer food category
bool _containsFoodShoppingPhrase(String text) {
  final foodShoppingPhrases = [
    'grocery shopping', 'food shopping', 'supermarket shopping',
    'restaurant shopping', 'cafe shopping'
  ];

  // Direct phrase matching
  if (foodShoppingPhrases.any((phrase) => text.contains(phrase))) {
    return true;
  }

  // Check for food-related words combined with shopping
  final foodWords = ['grocery', 'food', 'supermarket', 'restaurant', 'cafe'];
  final shoppingWords = ['shopping', 'shop', 'store'];

  // Check if text contains both food and shopping words
  final hasFoodWord = foodWords.any((word) => text.contains(word));
  final hasShoppingWord = shoppingWords.any((word) => text.contains(word));

  return hasFoodWord && hasShoppingWord;
}

/// Check for shopping-specific compound phrases that should prefer shopping category
bool _containsShoppingSpecificPhrase(String text) {
  final shoppingSpecificPhrases = [
    'clothes shopping', 'clothing shopping', 'electronics shopping',
    'gadget shopping', 'phone shopping', 'computer shopping',
    'fashion shopping', 'shoe shopping', 'jewelry shopping'
  ];
  return shoppingSpecificPhrases.any((phrase) => text.contains(phrase));
}

/// Get keywords for a specific category
List<String> getKeywordsForCategory(String categoryId) {
  return categoryKeywords[categoryId] ?? [];
}

/// Get all available category IDs that have keywords
List<String> getAllCategoryIds() {
  return categoryKeywords.keys.toList();
}

/// Calculate the score for a specific category given text input
/// Returns a score >= 0, where higher scores indicate better matches
double calculateCategoryScore(String text, String categoryId) {
  if (text.isEmpty || !categoryKeywords.containsKey(categoryId)) return 0.0;

  final normalizedText = text.toLowerCase().trim();
  final words = normalizedText.split(RegExp(r'\s+'));
  final keywords = categoryKeywords[categoryId]!;

  // Debug for specific categories
  if ((categoryId == 'business' || categoryId == 'shopping') && text == 'target supplies') {
    print('DEBUG: $categoryId keywords: ${keywords.take(10).toList()}');
    print('DEBUG: words: $words');
  }

  double score = 0;
  final matched = <String>[];

  // Check if any keyword matches
  for (final keyword in keywords) {
    final keywordLower = keyword.toLowerCase();



    // Brand/proper noun exact matches get very high score (check first)
    if (keywordLower.length > 3 && _isBrandName(keywordLower)) {
      if (normalizedText.contains(keywordLower)) {
        score += 15;
        matched.add(keywordLower);
        continue;
      }
    }

    // Exact word match gets highest score
    if (words.contains(keywordLower)) {
      // Give extra points for longer/more specific keywords
      double baseScore = 10 + (keywordLower.length > 5 ? 3 : 0);

      // Apply category-specific bonuses for food-related keywords
      if (categoryId == 'food' && _isFoodSpecificKeyword(keywordLower)) {
        baseScore += 5; // Bonus for food-specific keywords like 'grocery'
      }

      score += baseScore;
      matched.add(keywordLower);
      continue;
    }

    // Partial word match gets medium score (with improved precision)
    bool partialMatch = false;
    for (final word in words) {
      if (word.length >= 3 && keywordLower.length >= 3) {
        // More restrictive partial matching to avoid false positives
        bool isValidPartialMatch = false;

        if (word.contains(keywordLower)) {
          // Keyword is contained in word - check if it's a meaningful match
          final ratio = keywordLower.length / word.length;
          // Require keyword to be at least 60% of the word length, or be a prefix/suffix (min 5 chars)
          if (ratio >= 0.6 || (keywordLower.length >= 5 && (word.startsWith(keywordLower) || word.endsWith(keywordLower)))) {
            isValidPartialMatch = true;
          }
        } else if (keywordLower.contains(word)) {
          // Word is contained in keyword - check if it's a meaningful match
          final ratio = word.length / keywordLower.length;
          // Require word to be at least 60% of the keyword length, or be a prefix/suffix (min 5 chars)
          if (ratio >= 0.6 || (word.length >= 5 && (keywordLower.startsWith(word) || keywordLower.endsWith(word)))) {
            isValidPartialMatch = true;
          }
        }

        if (isValidPartialMatch) {
          score += 6;
          matched.add(keywordLower);
          partialMatch = true;
          break;
        }
      }
    }

    if (partialMatch) continue;

    // Full text contains keyword gets low score
    if (normalizedText.contains(keywordLower) && keywordLower.length >= 3) {
      score += 2;
      matched.add(keywordLower);
    }
  }

  // Apply compound phrase detection and priority rules
  if (score > 0) {
    // Detect compound phrases and apply bonuses
    if (_hasCompoundPhrase(normalizedText, categoryId, matched)) {
      score += 8; // Bonus for compound phrases
    }

    // Apply category priority rules for conflicts (simplified for single category)
    if (_shouldApplyCategoryPriorityForSingle(normalizedText, categoryId)) {
      score += 6; // Priority bonus
    }
  }

  return score;
}

/// Simplified category priority check for single category scoring
bool _shouldApplyCategoryPriorityForSingle(String text, String categoryId) {
  // Apply some basic priority rules for common conflicts
  switch (categoryId) {
    case 'food':
      return _containsFoodShoppingPhrase(text) ||
             text.contains('grocery') ||
             text.contains('restaurant') ||
             text.contains('cafe');
    case 'transport':
      return text.contains('uber') ||
             text.contains('taxi') ||
             text.contains('gas') ||
             text.contains('fuel');
    case 'shopping':
      return _containsShoppingSpecificPhrase(text);
    default:
      return false;
  }
}
