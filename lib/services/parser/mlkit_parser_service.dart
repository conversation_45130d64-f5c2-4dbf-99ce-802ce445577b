
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../models/amount_candidate.dart';
import '../../utils/currency_utils.dart';
import '../../utils/amount_utils.dart';
import '../../utils/raw_number_finder.dart';
import '../storage_service.dart';
import 'category_finder_service.dart';
import 'fallback_parser_service.dart';
import 'learned_association_service.dart';
import 'entity_extractor_base.dart';
import 'real_entity_extractor.dart';

/// Main orchestrator service that coordinates the entire parsing pipeline using ML Kit with fallback
class MlKitParserService {
  static MlKitParserService? _instance;

  EntityExtractorBase? _entityExtractor;
  late CategoryFinderService _categoryFinder;
  late FallbackParserService _fallbackParser;
  late StorageService _storageService;
  late LearnedAssociationService _learnedAssociationService;
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;
  bool _mlKitAvailable = false;

  MlKitParserService._();

  /// Factory constructor to get singleton instance
  /// [entityExtractor] - Optional entity extractor for dependency injection (mainly for testing)
  static Future<MlKitParserService> getInstance(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = _createInstance(storageService);
      await _instance!._initialize(entityExtractor);
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance?._entityExtractor?.close();
    _instance = null;
  }

  /// Static background initialization method for deferred loading
  static Future<MlKitParserService> initializeInBackground(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = _createInstance(storageService);
      await _instance!._initializeWithCaching(entityExtractor);
    }
    return _instance!;
  }

  /// Helper method to create and set up common instance properties
  static MlKitParserService _createInstance(StorageService storageService) {
    final instance = MlKitParserService._();
    instance._storageService = storageService;
    instance._categoryFinder = CategoryFinderService(storageService);
    instance._fallbackParser = FallbackParserService(storageService);
    // Initialize learned association service asynchronously
    LearnedAssociationService.getInstance(storageService).then((service) {
      instance._learnedAssociationService = service;
    });
    return instance;
  }

  /// Check if ML Kit is fully initialized and ready
  bool get isReady => _isInitialized && _mlKitAvailable && _entityExtractor != null;

  /// Get the category finder service for advanced category suggestions
  CategoryFinderService get categoryFinder => _categoryFinder;

  /// Initialize ML Kit models with fallback support
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initialize([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      debugPrint('Using injected entity extractor');
      _isInitialized = true;
      return;
    }

    try {
      debugPrint('Attempting to initialize ML Kit...');
      // Create and initialize the real entity extractor with StorageService for caching
      final realExtractor = RealEntityExtractor(_storageService);
      await realExtractor.initialize();

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      debugPrint('ML Kit initialized successfully');

    } catch (e) {
      debugPrint('Error initializing ML Kit: $e');
      debugPrint('Falling back to regex-based parsing');
      _mlKitAvailable = false;
      // ML Kit will be null, will use fallback parser
    }

    _isInitialized = true;
  }

  /// Initialize ML Kit models with enhanced caching support for background initialization
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initializeWithCaching([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      debugPrint('Using injected entity extractor');
      _isInitialized = true;
      return;
    }

    try {
      debugPrint('Attempting to initialize ML Kit with caching...');
      // Create and initialize the real entity extractor with StorageService for caching
      final realExtractor = RealEntityExtractor(_storageService);
      await realExtractor.initialize();

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      debugPrint('ML Kit initialized successfully with caching');

    } catch (e) {
      debugPrint('Error initializing ML Kit with caching: $e');
      debugPrint('Falling back to regex-based parsing');
      _mlKitAvailable = false;
      // ML Kit will be null, will use fallback parser
    }

    _isInitialized = true;
  }

  /// Main entry point for parsing transactions
  Future<ParseResult> parseTransaction(String text) async {
    print('MLKIT_DEBUG: parseTransaction ENTRY POINT called with: "$text"');
    try {
      if (!_isInitialized) {
        await _initialize();
      }

      // Handle empty or whitespace-only input
      if (text.trim().isEmpty) {
        return ParseResult.failed(
          await _createFallbackTransaction(text),
          'Empty or whitespace-only input',
        );
      }

      // NEW: Check learned associations first
      final learnedAssociation = await _learnedAssociationService.getAssociation(text);
      if (learnedAssociation != null) {
        final transaction = await _buildTransactionFromAssociation(text, learnedAssociation);
        return ParseResult.success(transaction);
      }

      // Always try ML Kit parsing logic first (even if entity extraction is not available)
      // This ensures we get multiple number detection and other advanced features
      print('MLKIT_DEBUG: Using ML Kit parsing logic (entity extraction available: ${_mlKitAvailable && _entityExtractor != null})');
      try {
        final mlResult = await _parseWithMLKit(text);
        // If ML Kit parsing triggers amount confirmation, return immediately
        if (mlResult.status == ParseStatus.needsAmountConfirmation) {
          return mlResult;
        }
        // If ML Kit succeeds or requires other input, return the result
        if (mlResult.status != ParseStatus.failed) {
          return mlResult;
        }
        // If ML Kit parsing logic fails, fall through to simple fallback
        print('MLKIT_DEBUG: ML Kit parsing logic failed, falling back to simple regex...');
      } catch (e) {
        print('MLKIT_DEBUG: ML Kit parsing logic failed with error, falling back to simple regex: $e');
        // Fall through to regex fallback
      }

      // Use fallback parser (regex-based) only as last resort
      print('MLKIT_DEBUG: Using simple fallback parser for text: "$text"');
      return await _fallbackParser.parseTransaction(text);

    } catch (e) {
      return ParseResult.failed(
        await _createFallbackTransaction(text),
        'Parsing failed: $e',
      );
    }
  }

  /// Parse using ML Kit with Trust but Verify approach
  /// Implements the PRD's 4-step approach:
  /// 1. Call ML Kit service for money entities
  /// 2. Independently run raw number finder on original text
  /// 3. Consolidate both lists into comprehensive candidates
  /// 4. Apply ambiguity detection to consolidated list
  Future<ParseResult> _parseWithMLKit(String text) async {
    print('MLKIT_DEBUG: _parseWithMLKit called with text: "$text"');

    // Step 1: Extract entities using ML Kit (if available)
    List<EntityAnnotationBase> entities = [];
    if (_mlKitAvailable && _entityExtractor != null) {
      try {
        entities = await _entityExtractor!.annotateText(text);
        print('MLKIT_DEBUG: ML Kit found ${entities.length} entities: ${entities.map((e) => '${e.entityType}:${e.text}').toList()}');
      } catch (e) {
        print('MLKIT_DEBUG: ML Kit extraction failed: $e, continuing with raw number finder only');
        // Continue with empty entities list - we'll still use raw number finder
      }
    } else {
      print('MLKIT_DEBUG: ML Kit entity extraction not available, using raw number finder only');
    }

    // Step 1: Convert ML Kit entities to AmountCandidate objects
    final List<AmountCandidate> mlKitCandidates = [];
    DateTime? extractedDate;
    String remainingText = text;

    for (final entity in entities) {
      if (entity.entityType == EntityType.money) {
        debugPrint('DEBUG: Processing ML Kit money entity: "${entity.text}"');

        // Parse the money entity (without filtering embedded numbers)
        final result = _parseMoneyEntityToCandidate(entity, text);
        if (result != null) {
          mlKitCandidates.add(result);
          debugPrint('DEBUG: Added ML Kit candidate: ${result.amount} (${result.currency})');
        } else {
          debugPrint('DEBUG: Failed to parse ML Kit money entity: "${entity.text}"');
        }
        remainingText = _removeEntityFromText(remainingText, entity);
      } else if (entity.entityType == EntityType.dateTime) {
        extractedDate = _parseDateTimeEntity(entity);
        remainingText = _removeEntityFromText(remainingText, entity);
        debugPrint('DEBUG: Extracted date: $extractedDate');
      }
    }

    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');

    // Step 2: Independently run raw number finder on original text
    final List<AmountCandidate> rawCandidates = RawNumberFinder.findAllNumbers(text);
    print('MLKIT_DEBUG: Raw finder found ${rawCandidates.length} candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    // Step 3: Consolidate both lists into comprehensive candidates
    final List<AmountCandidate> consolidatedCandidates = _consolidateCandidates(mlKitCandidates, rawCandidates);
    print('MLKIT_DEBUG: Consolidated candidates: ${consolidatedCandidates.map((c) => c.amount).toList()}');

    // Step 4: Apply ambiguity detection to consolidated list
    final ambiguityResult = await _detectAmountAmbiguityFromCandidates(consolidatedCandidates, text);
    if (ambiguityResult != null) {
      print('MLKIT_DEBUG: Ambiguity detected, triggering amount confirmation');
      return ambiguityResult;
    }

    print('MLKIT_DEBUG: No ambiguity detected, proceeding with best candidate selection');

    // If no ambiguity detected, select the best candidate
    final selectedCandidate = _selectBestAmountFromCandidates(consolidatedCandidates, text);
    if (selectedCandidate == null) {
      debugPrint('No valid amount candidate found, returning missingAmount');
      final fallbackTransaction = await _createFallbackTransaction(text);
      return ParseResult.missingAmount(
        fallbackTransaction,
        ambiguityType: AmbiguityType.missingAmount,
      );
    }

    double finalAmount = selectedCandidate.amount;
    String? finalCurrency = selectedCandidate.currency;

    // If no currency found, get default currency
    if (finalCurrency == null) {
      finalCurrency = await _storageService.getDefaultCurrency();
    }

    // Detect transaction type with negative amount information
    final isNegativeAmount = text.trim().startsWith('-');
    final type = _detectTransactionType(text, isNegativeAmount: isNegativeAmount);

    // If transaction type is unclear, return needsType status
    if (type == null) {
      // Create partial transaction with default expense type for type disambiguation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: finalAmount,
        type: TransactionType.expense, // Default type, will be updated by user selection
        categoryId: 'other',
        date: extractedDate ?? DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: finalCurrency,
      );

      return ParseResult.needsType(partialTransaction, ambiguityType: AmbiguityType.ambiguousType);
    }

    // Find category using the category finder service
    final categoryId = await _categoryFinder.findCategory(remainingText, type);

    // Create the transaction - use 'unknown' placeholder when no category found
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: finalAmount,
      type: type,
      categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
      date: extractedDate ?? DateTime.now(),
      description: _createDescription(text),
      tags: _extractTags(text),
      currencyCode: finalCurrency,
    );

    // Return result indicating if category selection is needed
    if (categoryId == null) {
      return ParseResult.needsCategory(transaction, ambiguityType: AmbiguityType.ambiguousCategory);
    } else {
      return ParseResult.success(transaction);
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    await _learnedAssociationService.learn(text, categoryId: categoryId);
  }

  /// Complete transaction parsing with user-confirmed amount
  Future<ParseResult> completeTransaction(String originalText, double confirmedAmount) async {
    debugPrint('DEBUG: completeTransaction called with text: "$originalText", amount: $confirmedAmount');

    try {
      // Learn the confirmed amount association
      await _learnedAssociationService.learn(originalText, confirmedAmount: confirmedAmount);
      debugPrint('DEBUG: Learned amount association for future use');

      // Determine transaction type
      final type = _detectTransactionType(originalText) ?? TransactionType.expense;
      debugPrint('DEBUG: Detected transaction type: $type');

      // Find category
      final categoryId = await _categoryFinder.findCategory(originalText, type);
      debugPrint('DEBUG: Found category: $categoryId');

      // Extract currency from original text
      final currency = _extractCurrencyFromText(originalText) ?? await _storageService.getDefaultCurrency();
      debugPrint('DEBUG: Using currency: $currency');

      // Create the final transaction
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: confirmedAmount,
        type: type,
        categoryId: categoryId ?? 'unknown',
        date: DateTime.now(),
        description: originalText.trim(),
        tags: _extractTags(originalText),
        currencyCode: currency,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        debugPrint('DEBUG: Transaction completed but needs category selection');
        return ParseResult.needsCategory(transaction, ambiguityType: AmbiguityType.ambiguousCategory);
      } else {
        debugPrint('DEBUG: Transaction completed successfully with amount: $confirmedAmount');
        return ParseResult.success(transaction);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to complete transaction: $e');
      return ParseResult.failed(
        await _createFallbackTransaction(originalText),
        'Failed to complete transaction: $e',
        ambiguityType: AmbiguityType.missingAmount,
      );
    }
  }







  /// Check if the detected amount appears within an alphabetic sequence like "Lux68" or "Restaurant123"
  /// Uses entity start/end positions for accurate detection instead of unreliable indexOf searches
  bool _hasAbbreviation(String sourceText) {
    // Check if the text contains abbreviations like k, K, m, M, b, B
    return RegExp(r'\d+\.?\d*[kKmMbB]$').hasMatch(sourceText.trim());
  }

  /// Extract and handle abbreviation patterns for improved search matching
  String _getSearchPatternForAbbreviation(String sourceText, String numericValue, String textToSearch) {
    // Special handling for large numbers that might be abbreviations
    // If numericValue is very large (like 2500000 from "2.5M"), try to find the original abbreviation
    if (sourceText.contains('k') || sourceText.contains('K') ||
        sourceText.contains('m') || sourceText.contains('M') ||
        sourceText.contains('b') || sourceText.contains('B')) {
      // Already has abbreviation in sourceText
      return sourceText;
    } else {
      // Check if this might be an expanded abbreviation by looking for patterns in fullText
      final abbreviationPattern = RegExp(r'\d+\.?\d*[kKmMbB]\b');
      final abbreviationMatch = abbreviationPattern.firstMatch(textToSearch);
      
      if (abbreviationMatch != null) {
        // Parse the abbreviation to see if it matches our numeric value
        final abbreviationText = abbreviationMatch.group(0)!;
        try {
          final abbreviationValue = AmountUtils.parseAbbreviatedNumber(abbreviationText);
          if (abbreviationValue != null && (abbreviationValue - double.parse(numericValue)).abs() < 0.01) {
            // This is the original abbreviation for our large number
            debugPrint('DEBUG: Found original abbreviation "$abbreviationText" for expanded value "$numericValue"');
            return abbreviationText;
          }
        } catch (e) {
          // Fall through to default numeric part
        }
      }
    }
    
    // Extract just the numeric part for searching (remove .0 if present)
    String numericPart = numericValue.toString();
    if (numericPart.endsWith('.0')) {
      numericPart = numericPart.replaceAll('.0', '');
    }
    return numericPart;
  }

  /// Count surrounding letters around a pattern in text
  int _countSurroundingLetters(String textToSearch, int patternIndex, int patternLength) {
    final beforeIndex = patternIndex - 1;
    final afterIndex = patternIndex + patternLength;
    
    final hasLetterBefore = beforeIndex >= 0 &&
        RegExp(r'[A-Za-z]').hasMatch(textToSearch[beforeIndex]);
    final hasLetterAfter = afterIndex < textToSearch.length &&
        RegExp(r'[A-Za-z]').hasMatch(textToSearch[afterIndex]);
    
    debugPrint('DEBUG: patternIndex=$patternIndex, beforeIndex=$beforeIndex, afterIndex=$afterIndex');
    debugPrint('DEBUG: hasLetterBefore=$hasLetterBefore, hasLetterAfter=$hasLetterAfter');
    
    // Count surrounding letters only if there are any
    if (!hasLetterBefore && !hasLetterAfter) {
      debugPrint('DEBUG: No surrounding letters, returning 0');
      return 0;
    }
    
    int letterCount = 0;
    
    // Count letters before the number
    if (hasLetterBefore) {
      for (int i = beforeIndex; i >= 0 && RegExp(r'[A-Za-z]').hasMatch(textToSearch[i]); i--) {
        letterCount++;
      }
    }
    
    // Count letters after the number
    if (hasLetterAfter) {
      for (int i = afterIndex; i < textToSearch.length && RegExp(r'[A-Za-z]').hasMatch(textToSearch[i]); i++) {
        letterCount++;
      }
    }
    
    debugPrint('DEBUG: Total letter count: $letterCount');
    return letterCount;
  }

  bool _isEmbeddedInVendorName(String sourceText, String numericValue, {String? fullText}) {
    // Use sourceText-based matching. If fullText is provided, search within it
    final textToSearch = fullText ?? sourceText;
    debugPrint('DEBUG: Checking embedded for sourceText="$sourceText", numericValue="$numericValue", searching in: "$textToSearch"');
    
    final searchPattern = _getSearchPatternForAbbreviation(sourceText, numericValue, textToSearch);
    debugPrint('DEBUG: Using searchPattern="$searchPattern"');
    
    // Find the position of the search pattern in the text
    final patternIndex = textToSearch.indexOf(searchPattern);
    if (patternIndex == -1) {
      debugPrint('DEBUG: Pattern not found in text, returning false');
      return false;
    }
    
    // Count surrounding letters using the extracted helper method
    final letterCount = _countSurroundingLetters(textToSearch, patternIndex, searchPattern.length);
    
    // Lowered threshold from 3 to 2 letters for better embedded detection
    final isEmbedded = letterCount >= 2;
    debugPrint('DEBUG: isEmbedded=$isEmbedded (threshold: 2+ letters)');
    
    return isEmbedded;
  }







  /// Format amount for display in confirmation UI
  String _formatAmountForDisplay(double amount) {
    if (amount >= 1000000000) {
      final billions = amount / 1000000000;
      return billions == billions.toInt() ? '${billions.toInt()}b' : '${billions.toStringAsFixed(1)}b';
    } else if (amount >= 1000000) {
      final millions = amount / 1000000;
      return millions == millions.toInt() ? '${millions.toInt()}m' : '${millions.toStringAsFixed(1)}m';
    } else if (amount >= 1000) {
      final thousands = amount / 1000;
      return thousands == thousands.toInt() ? '${thousands.toInt()}k' : '${thousands.toStringAsFixed(1)}k';
    } else {
      return amount == amount.toInt() ? amount.toInt().toString() : amount.toString();
    }
  }

  /// Parse datetime entity from ML Kit
  DateTime? _parseDateTimeEntity(EntityAnnotationBase entity) {
    try {
      // For now, we'll use the current date as ML Kit entity extraction
      // doesn't directly provide parsed DateTime objects in this version
      return DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Remove entity text from the remaining text
  String _removeEntityFromText(String text, EntityAnnotationBase entity) {
    final start = entity.start;
    final end = entity.end;
    
    if (start >= 0 && end <= text.length && end > start) {
      return (text.substring(0, start) + text.substring(end)).trim();
    }
    return text;
  }



  /// Extract currency information from text
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }



  /// Detect transaction type (from original parser)
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final normalizedText = text.toLowerCase().trim();

    if (RegExp(r'^\s*-').hasMatch(normalizedText) || isNegativeAmount) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
        .hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(normalizedText)) {
      return TransactionType.income;
    }
    
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(normalizedText)) {
      return TransactionType.loan;
    }
    
    if (RegExp(r'\bfor\b').hasMatch(normalizedText) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'[$€£¥]').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Future<Transaction> _createFallbackTransaction(String text, {double? amount}) async {
    final defaultCurrency = await _storageService.getDefaultCurrency();
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: defaultCurrency,
    );
  }

  /// Build transaction from learned association
  Future<Transaction> _buildTransactionFromAssociation(String text, LearnedAssociation association) async {
    // Use confirmed amount from association if available, otherwise extract from text
    double amount = 0.0;

    // Get default currency from storage instead of hardcoding USD
    final defaultCurrency = await _storageService.getDefaultCurrency();

    // Prioritize confirmed amount from learned association
    if (association.confirmedAmount != null) {
      amount = association.confirmedAmount!;
    } else {
      // Enhanced amount extraction for learned associations with abbreviation support
      final amountResult = AmountUtils.extractAmountFromText(text);
      if (amountResult != null) {
        amount = amountResult['amount'] as double;
      }
    }

    // Extract currency from text, fallback to default currency
    String currencyCode = _extractCurrencyFromText(text) ?? defaultCurrency;

    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: association.type ?? TransactionType.expense,
      categoryId: association.categoryId ?? 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: currencyCode,
    );
  }

  /// Parse money entity from ML Kit and convert to AmountCandidate
  /// This method does NOT filter out embedded numbers - that's handled later
  AmountCandidate? _parseMoneyEntityToCandidate(EntityAnnotationBase entity, String fullText) {
    try {
      // Extract the numeric value and currency from the money entity text
      final entityText = entity.text;

      // Enhanced regex pattern to include abbreviations: k/K, m/M, b/B
      final numericRegex = RegExp(r'(\d+(?:,\d{3})*(?:\.\d+)?[kKmMbB]?|\d+\.\d+[kKmMbB]?)');
      final match = numericRegex.firstMatch(entityText);
      double? amount;
      String? currency;

      if (match != null) {
        final numericString = match.group(1)!.replaceAll(',', '');

        // Use AmountUtils.parseAbbreviatedNumber() when abbreviation suffixes are detected
        if (numericString.toLowerCase().contains(RegExp(r'[kmb]$'))) {
          amount = AmountUtils.parseAbbreviatedNumber(numericString);
        } else {
          amount = double.tryParse(numericString);
        }
      }

      // Try to extract currency from the text with context
      currency = _extractCurrencyFromText(entityText);

      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: currency,
          start: entity.start,
          end: entity.end,
          sourceText: entityText,
        );
      }
    } catch (e) {
      // Fallback to text parsing - handle thousands separators properly
      final cleanText = entity.text.replaceAll(RegExp(r'[^\d.,]'), '');
      // Remove thousands separators (commas) but preserve decimal point
      final normalizedText = cleanText.replaceAll(',', '');
      final amount = double.tryParse(normalizedText);
      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: _extractCurrencyFromText(entity.text),
          start: entity.start,
          end: entity.end,
          sourceText: entity.text,
        );
      }
    }
    return null;
  }

  /// Consolidate ML Kit and raw number finder candidates
  /// Removes duplicates and merges the lists
  List<AmountCandidate> _consolidateCandidates(
    List<AmountCandidate> mlKitCandidates,
    List<AmountCandidate> rawCandidates,
  ) {
    debugPrint('DEBUG: _consolidateCandidates called');
    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');
    debugPrint('DEBUG: Raw candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    final List<AmountCandidate> consolidated = [];

    // Add all ML Kit candidates first
    consolidated.addAll(mlKitCandidates);
    debugPrint('DEBUG: Added ${mlKitCandidates.length} ML Kit candidates to consolidated list');

    // Add raw candidates that don't duplicate ML Kit candidates
    for (final rawCandidate in rawCandidates) {
      bool isDuplicate = false;

      for (final mlKitCandidate in mlKitCandidates) {
        // Check if they represent the same amount and position
        if (mlKitCandidate == rawCandidate) {
          isDuplicate = true;
          debugPrint('DEBUG: Raw candidate ${rawCandidate.amount} is duplicate of ML Kit candidate');
          break;
        }
      }

      if (!isDuplicate) {
        consolidated.add(rawCandidate);
        debugPrint('DEBUG: Added unique raw candidate: ${rawCandidate.amount}');
      }
    }

    // Sort by position for consistent ordering
    consolidated.sort((a, b) => a.start.compareTo(b.start));

    debugPrint('DEBUG: Final consolidated list: ${consolidated.map((c) => c.amount).toList()}');
    return consolidated;
  }

  /// Apply ambiguity detection to consolidated candidates
  /// Simple rule: If there are multiple different amounts, trigger ambiguity
  Future<ParseResult?> _detectAmountAmbiguityFromCandidates(
    List<AmountCandidate> candidates,
    String text,
  ) async {
    print('MLKIT_DEBUG: _detectAmountAmbiguityFromCandidates called with ${candidates.length} candidates');
    print('MLKIT_DEBUG: Total candidates: ${candidates.map((c) => c.amount).toList()}');

    // Check for missing amount case
    if (candidates.isEmpty) {
      debugPrint('DEBUG: No valid amount candidates found - returning missingAmount');
      final fallbackTransaction = await _createFallbackTransaction(text);
      return ParseResult.missingAmount(
        fallbackTransaction,
        ambiguityType: AmbiguityType.missingAmount,
      );
    }

    if (candidates.length == 1) {
      debugPrint('DEBUG: No ambiguity - only 1 candidate');
      return null; // No ambiguity with 1 candidate
    }

    // Remove duplicates based on amount to get unique amounts
    final uniqueCandidates = <AmountCandidate>[];
    for (final candidate in candidates) {
      bool isDuplicate = false;
      for (final existing in uniqueCandidates) {
        if (existing.hasSameAmount(candidate)) {
          isDuplicate = true;
          debugPrint('DEBUG: Duplicate amount found: ${candidate.amount} (skipping)');
          break;
        }
      }
      if (!isDuplicate) {
        uniqueCandidates.add(candidate);
        debugPrint('DEBUG: Added unique candidate: ${candidate.amount}');
      }
    }

    debugPrint('DEBUG: Unique candidates after deduplication: ${uniqueCandidates.map((c) => c.amount).toList()}');

    // Simple rule: If there are multiple unique amounts, it's ambiguous
    if (uniqueCandidates.length > 1) {
      print('MLKIT_DEBUG: Multiple unique amounts detected - triggering ambiguous amount');
      print('MLKIT_DEBUG: Final decision: AMBIGUOUS AMOUNT DETECTED');

      final amounts = uniqueCandidates.map((c) => c.amount).toList();
      final texts = uniqueCandidates.map((c) => _formatAmountForDisplay(c.amount)).toList();

      // Get currency from candidates or extract from text
      String currency = await _storageService.getDefaultCurrency(); // Use user's default currency

      // First try to get currency from candidates
      for (final candidate in uniqueCandidates) {
        if (candidate.currency != null) {
          currency = candidate.currency!;
          break;
        }
      }

      // If no candidate has currency, extract from full text
      final extractedCurrency = _extractCurrencyFromText(text);
      if (extractedCurrency != null) {
        currency = extractedCurrency;
      }

      // Create partial transaction for confirmation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: amounts.first, // Temporary amount
        type: _detectTransactionType(text) ?? TransactionType.expense,
        categoryId: 'unknown',
        date: DateTime.now(),
        description: text.trim(),
        tags: _extractTags(text),
        currencyCode: currency,
      );

      return ParseResult.ambiguousAmount(
        partialTransaction,
        amounts,
        texts,
        ambiguityType: AmbiguityType.ambiguousAmount,
      );
    }

    debugPrint('DEBUG: Only one unique amount found - no ambiguity');
    return null; // No ambiguity detected
  }

  /// Select the best amount candidate from consolidated list
  AmountCandidate? _selectBestAmountFromCandidates(
    List<AmountCandidate> candidates,
    String text,
  ) {
    if (candidates.isEmpty) return null;
    if (candidates.length == 1) return candidates.first;

    // Prefer non-embedded candidates
    final nonEmbeddedCandidates = candidates.where((candidate) =>
        !_isEmbeddedInVendorName(candidate.sourceText, candidate.amount.toString(), fullText: text)).toList();

    if (nonEmbeddedCandidates.isNotEmpty) {
      // Among non-embedded candidates, prefer those with abbreviations
      final abbreviatedCandidates = nonEmbeddedCandidates.where((candidate) =>
          _hasAbbreviation(candidate.sourceText)).toList();

      if (abbreviatedCandidates.isNotEmpty) {
        return abbreviatedCandidates.first;
      }

      return nonEmbeddedCandidates.first;
    }

    // Fall back to first candidate if all are embedded
    return candidates.first;
  }

  /// Dispose resources
  void dispose() {
    _entityExtractor?.close();
  }
}
