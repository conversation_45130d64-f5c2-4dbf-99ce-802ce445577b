import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/transaction_model.dart';
import '../models/category_suggestion.dart';
import '../services/parser/category_finder_service.dart';

class CategoryPickerDialog extends StatefulWidget {
  final TransactionType transactionType;
  final String? initialCategoryId;
  final String? transactionText; // For generating smart suggestions
  final CategoryFinderService? categoryFinderService;

  const CategoryPickerDialog({
    Key? key,
    required this.transactionType,
    this.initialCategoryId,
    this.transactionText,
    this.categoryFinderService,
  }) : super(key: key);

  @override
  State<CategoryPickerDialog> createState() => _CategoryPickerDialogState();
}

class _CategoryPickerDialogState extends State<CategoryPickerDialog> {
  String? _selectedCategoryId;
  String _searchQuery = '';
  late TextEditingController _searchController;
  CategorySuggestionResult? _suggestions;
  bool _isLoadingSuggestions = false;

  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.initialCategoryId;
    _searchController = TextEditingController();
    _loadSmartSuggestions();
  }

  /// Load smart suggestions if transaction text and category finder service are available
  Future<void> _loadSmartSuggestions() async {
    if (widget.transactionText != null &&
        widget.categoryFinderService != null &&
        widget.transactionText!.trim().isNotEmpty) {
      setState(() {
        _isLoadingSuggestions = true;
      });

      try {
        final suggestions = await widget.categoryFinderService!
            .findCategorySuggestions(widget.transactionText!, widget.transactionType);

        setState(() {
          _suggestions = suggestions;
          _isLoadingSuggestions = false;
        });
      } catch (e) {
        setState(() {
          _isLoadingSuggestions = false;
        });
      }
    }
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    
    // Filter categories by transaction type and search query
    final availableCategories = provider.categories
        .where((category) => category.type == widget.transactionType)
        .where((category) => 
            _searchQuery.isEmpty || 
            category.name.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();
    
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Select ${_getTransactionTypeLabel()} Category'),
          const SizedBox(height: 8),
          Text(
            'This will help improve automatic categorization for similar transactions.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400, // Fixed height for scrollable content
        child: Column(
          children: [
            // Search field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search categories',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Smart suggestions section
            if (_suggestions != null && _suggestions!.suggestions.isNotEmpty && _searchQuery.isEmpty)
              _buildSmartSuggestionsSection(theme, provider),

            // Categories grid
            Expanded(
              child: availableCategories.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 48,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No categories found',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          if (_searchQuery.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Try a different search term',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    )
                  : GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 2.5,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: availableCategories.length,
                      itemBuilder: (context, index) {
                        final category = availableCategories[index];
                        final isSelected = _selectedCategoryId == category.id;
                        
                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedCategoryId = category.id;
                            });
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: isSelected 
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.outline,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              color: isSelected 
                                  ? theme.colorScheme.primaryContainer.withOpacity(0.3)
                                  : null,
                            ),
                            padding: const EdgeInsets.all(12),
                            child: Row(
                              children: [
                                Text(
                                  category.icon,
                                  style: const TextStyle(fontSize: 20),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    category.name,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: isSelected 
                                          ? FontWeight.w600 
                                          : FontWeight.normal,
                                      color: isSelected 
                                          ? theme.colorScheme.primary
                                          : null,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _selectedCategoryId != null
              ? () => Navigator.of(context).pop(_selectedCategoryId)
              : null,
          child: const Text('Select'),
        ),
      ],
    );
  }
  
  String _getTransactionTypeLabel() {
    switch (widget.transactionType) {
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.income:
        return 'Income';
      case TransactionType.loan:
        return 'Loan';
    }
  }

  /// Build the smart suggestions section
  Widget _buildSmartSuggestionsSection(ThemeData theme, TransactionProvider provider) {
    final suggestions = _suggestions!.suggestions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.auto_awesome,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              'Smart Suggestions',
              style: theme.textTheme.labelMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: suggestions.length,
            itemBuilder: (context, index) {
              final suggestion = suggestions[index];
              final category = provider.getCategoryById(suggestion.categoryId);

              if (category == null) return const SizedBox.shrink();

              final isSelected = _selectedCategoryId == category.id;
              final confidenceColor = _getConfidenceColor(suggestion.confidence, theme);

              return Container(
                width: 120,
                margin: const EdgeInsets.only(right: 8),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedCategoryId = category.id;
                    });
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : confidenceColor.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      color: isSelected
                          ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
                          : confidenceColor.withValues(alpha: 0.1),
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          category.icon,
                          style: const TextStyle(fontSize: 20),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          category.name,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected ? theme.colorScheme.primary : null,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        _buildConfidenceIndicator(suggestion.confidence, theme),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        const Divider(),
        const SizedBox(height: 8),
      ],
    );
  }

  /// Build confidence indicator
  Widget _buildConfidenceIndicator(double confidence, ThemeData theme) {
    final confidenceColor = _getConfidenceColor(confidence, theme);
    final confidenceText = _getConfidenceText(confidence);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: confidenceColor,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          confidenceText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: confidenceColor,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Get color based on confidence level
  Color _getConfidenceColor(double confidence, ThemeData theme) {
    if (confidence >= 0.8) {
      return Colors.green;
    } else if (confidence >= 0.6) {
      return Colors.orange;
    } else {
      return theme.colorScheme.outline;
    }
  }

  /// Get confidence text
  String _getConfidenceText(double confidence) {
    if (confidence >= 0.8) {
      return 'High';
    } else if (confidence >= 0.6) {
      return 'Med';
    } else {
      return 'Low';
    }
  }
}

/// Utility function to show the category picker dialog
Future<String?> showCategoryPickerDialog({
  required BuildContext context,
  required TransactionType transactionType,
  String? initialCategoryId,
  String? transactionText,
  CategoryFinderService? categoryFinderService,
}) {
  return showDialog<String>(
    context: context,
    builder: (context) => CategoryPickerDialog(
      transactionType: transactionType,
      initialCategoryId: initialCategoryId,
      transactionText: transactionText,
      categoryFinderService: categoryFinderService,
    ),
  );
}
