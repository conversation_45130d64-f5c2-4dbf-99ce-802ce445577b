# Codebase Structure

This document provides a detailed overview of the DreamFlow application codebase organization.

**Project Name**: DreamFlow (configured as `dreamflow` in pubspec.yaml)
**Display Name**: Money Lover Chat (used in UI and main application class)
**Description**: A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Directory Structure

### Root Level

```
dreamflow/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── assets/           # Application assets (e.g., localization files)
│   └── l10n/
│       ├── en.json
│       └── es.json
├── docs/             # Project documentation
├── scripts/          # Build and utility scripts
├── pubspec.yaml      # Dependencies and project configuration
└── ...
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   ├── transaction_model.dart
│   ├── parse_result.dart
│   ├── localization_data.dart
│   └── amount_candidate.dart
├── services/         # Business logic and data services
│   ├── parser/       # Hybrid ML parsing module
│   │   ├── mlkit_parser_service.dart
│   │   ├── fallback_parser_service.dart
│   │   ├── learned_association_service.dart
│   │   ├── category_finder_service.dart
│   │   ├── category_keyword_map.dart
│   │   ├── learned_category_storage.dart
│   │   ├── entity_extractor_base.dart
│   │   └── real_entity_extractor.dart
│   ├── storage_service.dart
│   ├── localization_service.dart
│   └── transaction_parser_service.dart  # Legacy parser (deprecated)
├── screens/          # UI screens
│   ├── chat_screen.dart
│   ├── categories_screen.dart
│   ├── settings_screen.dart
│   └── statistics_screen.dart
├── widgets/          # Reusable UI components
│   ├── transaction_message.dart
│   ├── category_picker_dialog.dart
│   ├── quick_reply_widget.dart
│   └── transaction_edit_dialog.dart
├── utils/            # Utility functions
│   ├── currency_utils.dart
│   ├── amount_utils.dart
│   └── raw_number_finder.dart
├── navigation/       # Navigation logic
│   └── app_navigation.dart
├── main.dart         # Application entry point
├── theme.dart        # App theme configuration
├── audio_recorder.dart    # Audio recording functionality
├── file_upload.dart       # File upload utilities
├── image_upload.dart      # Image handling utilities
└── video_recorder.dart    # Video recording functionality
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models:
  - `Transaction`: Represents a financial transaction, including `amount` and `currencyCode`.
  - `Category`: Defines transaction categories.
  - `ChatMessage`: Represents a message in the chat, with support for quick replies.
  - `TransactionProvider`: Manages all transaction and chat message state using Provider.
- **parse_result.dart**:
  - `ParseResult`: A data transfer object from the parser service to the UI.
  - `ParseStatus`: An enum (`success`, `needsCategory`, `needsType`, `failed`) indicating the result of a parsing attempt.
- **localization_data.dart**:
  - `LocalizationData`: A model that holds the keywords and number formatting rules for a specific locale, loaded from the JSON asset files.
- **amount_candidate.dart**:
  - `AmountCandidate`: Represents a potential amount value found during parsing, with confidence scoring and source information.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`. It is the foundation for services that need to store data, like the `LearnedAssociationService`.
- **localization_service.dart**: Loads and provides localization data from JSON files in `assets/l10n/`. This service supplies the `FallbackParserService` with locale-specific keywords and number formats, making the regex parser adaptable to different languages.
- **parser/mlkit_parser_service.dart**: The primary service for parsing user input. It orchestrates a hybrid "Trust but Verify" system. It first consults the `LearnedAssociationService` for user-corrected patterns. If none are found, it runs ML Kit entity extraction and a custom `RawNumberFinder` in parallel. All potential amount candidates are consolidated, and if ambiguity is detected (e.g., a number in a vendor name and a separate amount), it triggers a "soft fail" to ask the user for clarification. It falls back to the `FallbackParserService` if the primary methods fail.
- **parser/fallback_parser_service.dart**: A regex-based parser that serves as a safety net when ML Kit is unavailable. It is designed to be fully localizable by dynamically building its regex patterns from the data provided by the `LocalizationService`.
- **parser/learned_association_service.dart**: The core of the app's learning capability. This service stores and retrieves associations between text patterns and transaction attributes (type, category). It learns from user corrections during "soft fails" and manual edits, allowing the app to bypass complex parsing for known user inputs, increasing speed and accuracy over time.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for formatting currency amounts and handling currency codes and symbols.
- **amount_utils.dart**: Contains utilities for amount parsing, validation, and candidate selection logic.
- **raw_number_finder.dart**: A utility that runs in parallel with ML Kit to extract all raw numeric values from text, ensuring no potential amount is missed. It is a key part of the "Trust but Verify" candidate consolidation strategy.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions. It interacts with the `MlKitParserService` and handles the conversational "soft fail" flows, which now trigger the `LearnedAssociationService`.
- **categories_screen.dart**: Allows users to manage transaction categories, organized by transaction type (expense, income, loan).
- **settings_screen.dart**: Allows users to configure app settings, including theme preferences and default currency.
- **statistics_screen.dart**: Displays financial statistics and data visualizations using charts and summary cards.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction within the chat history.
- **category_picker_dialog.dart**: A dialog for manually selecting a category when the parser cannot determine one automatically.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message, used for transaction type disambiguation.
- **transaction_edit_dialog.dart**: A dialog for editing existing transactions, including amount, category, and description changes.

### Multimedia Support (`lib/`)

- **audio_recorder.dart**: Handles audio recording functionality for voice memos attached to transactions.
- **file_upload.dart**: Provides file upload utilities for attaching documents to transactions.
- **image_upload.dart**: Manages image capture and selection from gallery for receipt documentation.
- **video_recorder.dart**: Handles video recording and playback functionality for transaction documentation.