# Architectural Decision Records

This document captures key architectural decisions made during the development of the DreamFlow application.

**Project**: DreamFlow (Money Lover Chat)
**Decision Record Format**: Lightweight ADR format
**Status**: Living document - updated as architecture evolves

## ADR 1: Provider for State Management

**Context**: The application needs a state management solution that is easy to learn, well-supported by Flutter, and efficient for our use case.

**Decision**: Use the Provider package for state management.

**Alternatives Considered**:
- BLoC/Cubit: More complex but has better separation of concerns
- GetX: Simpler API but less idiomatic to Flutter
- Redux: More verbose but more predictable state transitions

**Rationale**:
- Simplicity: Provider has a gentle learning curve
- Integration: Well-integrated with <PERSON>lut<PERSON>'s widget system
- Community: Strong community support and documentation
- Flexibility: Can be combined with other patterns as needed

**Consequences**:
- Positive: Rapid development cycle, less boilerplate code
- Negative: May be less structured for very complex state logic
- Risk: Potential for widget rebuilds if not properly optimized with selectors

## ADR 2: Shared Preferences for Local Storage

**Context**: The application needs to store user data and settings locally.

**Decision**: Use the Shared Preferences package for local data storage.

**Alternatives Considered**:
- SQLite: More powerful for relational data but requires more setup
- Hive: Faster but requires more setup
- Isar: Powerful but relatively new

**Rationale**:
- Simplicity: Easy to implement and use
- Performance: Sufficient for our current data storage needs
- Platform support: Works across all Flutter platforms

**Consequences**:
- Positive: Quick implementation for MVP
- Negative: Limited to simple key-value pairs
- Risk: May need migration to more robust solution as data needs grow

## ADR 3: Natural Language Transaction Entry

**Context**: Users need an intuitive way to enter financial transactions.

**Decision**: Implement a chat-based interface with natural language parsing for transaction entry.

**Alternatives Considered**:
- Traditional form-based entry
- Voice recognition
- QR/receipt scanning

**Rationale**:
- User experience: More natural interaction model
- Flexibility: Can support various transaction entry formats
- Differentiation: Unique feature compared to competing apps

**Consequences**:
- Positive: Enhanced user experience
- Negative: More complex implementation
- Risk: Need for robust parsing logic to handle varied input formats

## ADR 4: Layered Architecture

**Context**: The application needs a clear structural organization for maintainability.

**Decision**: Implement a layered architecture with clear separation between UI, business logic, services, and data layers.

**Alternatives Considered**:
- Feature-first architecture
- Domain-driven design
- Clean Architecture with more abstraction layers

**Rationale**:
- Clarity: Clear responsibilities for each layer
- Maintainability: Easier to understand and modify specific parts
- Testability: Easier to test individual components

**Consequences**:
- Positive: Better organization and maintainability
- Negative: Some code overhead for layer separation
- Risk: Potential for unnecessary abstraction if overdone

## ADR 5: Multimedia Support

**Context**: Users benefit from attaching media to transactions for receipt documentation.

**Decision**: Implement support for images, audio, video, and file attachments.

**Alternatives Considered**:
- Images only
- Third-party storage integration
- Cloud-based solution

**Rationale**:
- Flexibility: Provides users with multiple documentation options
- Completeness: Comprehensive transaction record keeping
- User experience: Enhanced functionality over competitors

**Consequences**:
- Positive: More robust transaction documentation
- Negative: Increased app complexity and storage requirements
- Risk: Potential performance impact with large media libraries

## ADR 6: Localizable Fallback Parser

**Context**: The regex-based fallback parser contained hardcoded English keywords and US-centric number formats. This made the fallback system completely non-functional for users in any other language, creating a jarring experience if the primary ML parser failed.

**Decision**: Refactor the `FallbackParserService` to be stateless and dynamically build its regex patterns at runtime. A new `LocalizationService` was created to load locale-specific keywords (e.g., "spent," "gasté") and number separators from simple JSON files stored in `assets/l10n/`.

**Rationale**:
- **Decouples Logic from Localization**: Removes language-specific data from the core application logic.
- **Maintainability**: Translators or developers can add or edit languages by simply editing a JSON file, without touching Dart code.
- **Scalability**: Provides a clear and simple architecture for supporting new languages in the fallback parser in the future.

**Consequences**:
- Positive: The fallback parser is now fully localizable. The app is more robust for international users.
- Negative: A small amount of overhead is introduced to load and parse the JSON file on first use for a locale.
- Action: New JSON files (e.g., `de.json`, `fr.json`) can be added to `assets/l10n/` to expand language support.

## ADR 7: Unified Learning System for Parsing

**Context**: The application had no long-term memory. When a user corrected a miscategorized transaction (e.g., changing "Netflix" from "Utilities" to "Entertainment"), the system would not remember this choice for the future, forcing the user to make the same correction repeatedly.

**Decision**: Implement a `LearnedAssociationService` that creates a persistent memory of user corrections. This service stores associations between a normalized text pattern (e.g., "netflix") and the user-assigned transaction attributes (e.g., `type: expense`, `categoryId: entertainment`). The main `MlKitParserService` now queries this service *before* attempting any other parsing. Learning is triggered by both "soft fail" resolutions and manual transaction edits.

**Rationale**:
- **Reduces User Friction**: Drastically decreases the number of prompts for recurring transactions.
- **Increases Speed & Accuracy**: For known text patterns, parsing is near-instant and 100% accurate based on user preference, bypassing slower ML/regex processing.
- **Builds User Confidence**: Creates a system that visibly adapts to the user's personal financial vocabulary, making the app feel more intelligent and trustworthy.

**Consequences**:
- **Positive**: The user experience becomes highly personalized. The system gets "smarter" and faster with use.
- **Positive**: The system learns from the most reliable signals: direct user corrections.
- **Risk**: The learning is based on the transaction's description text. If descriptions for different categories are too similar, the system might make incorrect assumptions.

## ADR 8: "Trust but Verify" Parsing for Amount Ambiguity

- **Context**: The parser struggled with inputs containing multiple numbers, especially when a number was part of a vendor name (e.g., "Dinner at Lux68 50k"). The initial ML Kit result might incorrectly identify "68" as the amount and ignore "50k", leading to incorrect parsing without a chance for recovery.
- **Decision**: Implement a "Trust but Verify" strategy. Instead of relying solely on ML Kit's initial output, the system performs two parallel extractions: one using ML Kit and another using a `RawNumberFinder` that extracts all numeric values. All candidates are then consolidated. If ambiguity exists in the consolidated list, the user is prompted to resolve it.
- **Rationale**:
    - **Robustness**: Ensures that no potential amount is missed, even if ML Kit's specialized model filters it out.
    - **Accuracy**: By consolidating all candidates, the system has a complete picture to make a better decision or to present the ambiguity to the user.
    - **User-Centric**: Instead of guessing wrong, the system gracefully fails and asks the user, which also feeds the learning system.
- **Consequences**:
    - Positive: Drastically improves parsing accuracy for complex inputs with multiple numbers. The system can now correctly handle cases it previously failed on.
    - Positive: The learning system becomes more powerful as it can learn from amount ambiguity resolutions.
    - Slight Negative: A minor increase in parsing complexity, but the improved accuracy and user experience justify it.