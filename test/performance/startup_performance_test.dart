import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/utils/startup_timer.dart';
import 'package:dreamflow/services/parser/mlkit_parser_service.dart';
import '../mocks/mock_storage_service.dart';

/// Performance tests for startup optimization
///
/// These tests measure the performance impact of the startup optimization changes
/// and ensure that the background initialization approach provides better user experience.
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Startup Performance Tests', () {
    late StartupTimer timer;
    
    setUp(() {
      timer = StartupTimer.instance;
      // Reset timer state before each test
      timer.mark('test-start');
    });
    
    tearDown(() {
      // Clean up after each test
      MlKitParserService.resetInstance();
    });

    test('StartupTimer measures performance correctly', () async {
      // Test the StartupTimer utility itself
      timer.mark('phase-1');
      
      // Simulate some work
      await Future.delayed(const Duration(milliseconds: 10));
      
      timer.mark('phase-2');
      
      // Simulate more work
      await Future.delayed(const Duration(milliseconds: 20));
      
      timer.mark('phase-3');
      
      // Verify measurements
      final duration1to2 = timer.measure('phase-1', 'phase-2');
      final duration2to3 = timer.measure('phase-2', 'phase-3');
      final totalDuration = timer.durationSince('phase-1');

      expect(duration1to2, isNotNull);
      expect(duration2to3, isNotNull);
      expect(totalDuration, isNotNull);
      expect(duration1to2!.inMicroseconds, greaterThan(0));
      expect(duration2to3!.inMicroseconds, greaterThan(0));
      expect(totalDuration!.inMicroseconds, greaterThan(0));
      expect(totalDuration.inMicroseconds, greaterThan(duration1to2.inMicroseconds));
      expect(totalDuration.inMicroseconds, greaterThan(duration2to3.inMicroseconds));
    });

    test('Background ML Kit initialization is non-blocking', () async {
      final storageService = MockStorageService();
      await storageService.init();
      
      timer.mark('background-init-start');
      
      // Start background initialization (should not block)
      final initFuture = MlKitParserService.initializeInBackground(storageService);
      
      timer.mark('background-init-dispatched');
      
      // Verify that dispatching the background initialization is fast
      final dispatchDuration = timer.measure('background-init-start', 'background-init-dispatched');
      expect(dispatchDuration, isNotNull);
      expect(dispatchDuration!.inMilliseconds, lessThan(50),
          reason: 'Background initialization dispatch should be very fast');

      // Wait for actual initialization to complete
      final service = await initFuture;
      timer.mark('background-init-complete');

      expect(service, isNotNull);

      // Print timing information for analysis
      final totalDuration = timer.measure('background-init-start', 'background-init-complete');
      print('Background initialization took: ${totalDuration?.inMilliseconds ?? 0}ms');
    });

    test('Storage service initialization is fast', () async {
      timer.mark('storage-init-start');
      
      final storageService = MockStorageService();
      await storageService.init();
      
      timer.mark('storage-init-complete');
      
      final duration = timer.measure('storage-init-start', 'storage-init-complete');

      // Storage initialization should be very fast (under 100ms)
      expect(duration, isNotNull);
      expect(duration!.inMilliseconds, lessThan(100),
          reason: 'Storage service initialization should be fast');

      print('Storage initialization took: ${duration.inMilliseconds}ms');
    });

    test('ML Kit service handles null state gracefully', () async {
      final storageService = MockStorageService();
      await storageService.init();
      
      // Test that we can create a service instance without immediate ML Kit initialization
      timer.mark('service-creation-start');
      
      final service = await MlKitParserService.getInstance(storageService);
      
      timer.mark('service-creation-complete');
      
      expect(service, isNotNull);
      
      // Service creation should be reasonably fast
      final duration = timer.measure('service-creation-start', 'service-creation-complete');
      print('Service creation took: ${duration?.inMilliseconds ?? 0}ms');
      
      // Test parsing with potentially uninitialized ML Kit
      final parseResult = await service.parseTransaction('spent \$25 on coffee');
      expect(parseResult, isNotNull);
      // Should either succeed or fail gracefully, not crash
    });

    test('Startup timer handles concurrent access', () async {
      // Test thread safety of StartupTimer
      final futures = <Future<void>>[];
      
      for (int i = 0; i < 10; i++) {
        futures.add(Future(() async {
          timer.mark('concurrent-$i');
          await Future.delayed(Duration(milliseconds: i * 2));
          timer.mark('concurrent-$i-end');
        }));
      }
      
      await Future.wait(futures);
      
      // Verify all markers were recorded
      for (int i = 0; i < 10; i++) {
        final duration = timer.measure('concurrent-$i', 'concurrent-$i-end');
        expect(duration, greaterThanOrEqualTo(Duration.zero));
      }
    });

    test('Performance regression detection', () async {
      // This test serves as a baseline for detecting performance regressions
      final storageService = MockStorageService();
      
      timer.mark('full-startup-simulation-start');
      
      // Simulate the startup sequence
      await storageService.init();
      timer.mark('storage-ready');
      
      // Simulate UI being shown (this should be fast)
      await Future.delayed(const Duration(milliseconds: 1));
      timer.mark('ui-shown');
      
      // Simulate background ML Kit initialization
      final service = await MlKitParserService.initializeInBackground(storageService);
      timer.mark('mlkit-ready');
      
      // Measure key intervals
      final storageTime = timer.measure('full-startup-simulation-start', 'storage-ready');
      final uiTime = timer.measure('storage-ready', 'ui-shown');
      final mlkitTime = timer.measure('ui-shown', 'mlkit-ready');
      final totalTime = timer.measure('full-startup-simulation-start', 'mlkit-ready');
      
      // Print performance metrics
      print('=== Startup Performance Metrics ===');
      print('Storage initialization: ${storageTime?.inMilliseconds ?? 0}ms');
      print('UI ready time: ${uiTime?.inMilliseconds ?? 0}ms');
      print('ML Kit background init: ${mlkitTime?.inMilliseconds ?? 0}ms');
      print('Total startup time: ${totalTime?.inMilliseconds ?? 0}ms');

      // Performance expectations (these may need adjustment based on actual performance)
      expect(storageTime, isNotNull);
      expect(uiTime, isNotNull);
      expect(totalTime, isNotNull);
      expect(storageTime!.inMilliseconds, lessThan(100),
          reason: 'Storage init should be under 100ms');
      expect(uiTime!.inMilliseconds, lessThan(10),
          reason: 'UI should be ready very quickly');
      // ML Kit can take longer but should complete eventually
      expect(totalTime!.inMilliseconds, lessThan(30000),
          reason: 'Total startup should complete within 30 seconds');
      
      expect(service, isNotNull);
    });
  });
}
